# EXTRACTION OF TEXT AND BBOX AND CODE

from pdfminer.high_level import extract_pages
from pdfminer.layout import LTTextBox, LTTextLine, LTChar
import re

code_pattern = re.compile(r'^\d{2}\.[A-Z]\.\d{2}$')
filtered_pairs = []
for page_layout in extract_pages("FP1.pdf"):
    for element in page_layout:
        if isinstance(element, LTTextBox) or isinstance(element, LTTextLine):
            text = element.get_text().strip()
            x0, y0, x1, y1 = element.bbox
            # print(f"Text: '{text}' at ({x0}, {y0}, {x1}, {y1})")
            lines = text.strip().splitlines()
            if len(lines) == 2 and code_pattern.match(lines[1].strip()):
                print(f"  - Label: '{lines[0]}' - Code: '{lines[1]}' - BBox: ({x0}, {y0}, {x1}, {y1})")
                filtered_pairs.append({
                    "label": lines[0].strip(),
                    "code": lines[1].strip(),
                    "bbox": ({x0}, {y0}, {x1}, {y1})
                })
                

