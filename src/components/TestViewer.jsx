import { useEffect, useRef } from 'react';

const TestViewer = ({ pdfUrl = '/FP1.pdf' }) => {
  const containerRef = useRef(null);

  useEffect(() => {
    const container = containerRef.current;
    let cleanup = () => { };

    (async () => {
      console.log('TestViewer mounted');
      console.log('Container:', container);
      console.log('PDF URL:', pdfUrl);

      if (!container || !pdfUrl) {
        console.log('No container or PDF URL available');
        return;
      }

      try {
        const NutrientViewer = (await import('@nutrient-sdk/viewer')).default;
        console.log('NutrientViewer imported:', NutrientViewer);

        // Ensure there's only one NutrientViewer instance
        NutrientViewer.unload(container);

        const loadConfig = {
          container,
          document: pdfUrl,
          baseUrl: `${window.location.protocol}//${window.location.host}/`,
          // To remove "FOR EVALUATION PURPOSE ONLY" watermark, add your license key:
          // licenseKey: 'YOUR_LICENSE_KEY_HERE'
        };

        console.log('Load config:', loadConfig);

        const instance = await NutrientViewer.load(loadConfig);
        console.log('PDF loaded successfully:', instance);

        cleanup = () => {
          NutrientViewer.unload(container);
        };
      } catch (error) {
        console.error('Error loading PDF:', error);
        console.error('Error stack:', error.stack);
        console.error('Error name:', error.name);
      }
    })();

    return cleanup;
  }, [pdfUrl]);

  return (
    <div className="w-full h-full">
      <div
        ref={containerRef}
        className="w-full h-full"
        style={{
          width: '100vw',
          height: '100vh'
        }}
      />
    </div>
  );
};

export default TestViewer;
