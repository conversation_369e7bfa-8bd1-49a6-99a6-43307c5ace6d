import { useState } from 'react';
import TestViewer from './components/TestViewer';

function App() {
  const [selectedFile, setSelectedFile] = useState(null);
  const [pdfUrl, setPdfUrl] = useState('/FP1.pdf'); // Default to the PDF in public folder

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
      // Clean up previous URL if it exists
      if (selectedFile && pdfUrl !== '/FP1.pdf') {
        URL.revokeObjectURL(pdfUrl);
      }
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPdfUrl(url);
    } else {
      alert('Please select a valid PDF file');
    }
  };

  const resetToDefault = () => {
    if (selectedFile && pdfUrl !== '/FP1.pdf') {
      URL.revokeObjectURL(pdfUrl);
    }
    setSelectedFile(null);
    setPdfUrl('/FP1.pdf');
  };

  return (
    <div className="h-screen w-screen relative">
      {/* Full Screen PDF Viewer */}
      <TestViewer pdfUrl={pdfUrl} />

      {/* Floating Upload Button */}
      <div className="fixed top-4 right-4 z-50 flex gap-2">
        {/* Upload PDF Button */}
        <div className="relative">
          <input
            type="file"
            accept=".pdf"
            onChange={handleFileUpload}
            className="hidden"
            id="pdf-upload"
          />
          <label
            htmlFor="pdf-upload"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg cursor-pointer transition-colors duration-200 flex items-center gap-2 shadow-lg"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            Upload PDF
          </label>
        </div>

        {/* Reset Button */}
        {selectedFile && (
          <button
            onClick={resetToDefault}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2 shadow-lg"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Reset
          </button>
        )}
      </div>

      {/* File Info Overlay */}
      {selectedFile && (
        <div className="fixed bottom-4 left-4 z-50 bg-black bg-opacity-75 text-white px-4 py-2 rounded-lg">
          <div className="text-sm">
            <div className="font-semibold">{selectedFile.name}</div>
            <div className="text-gray-300">Size: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</div>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
