{"cells": [{"cell_type": "code", "execution_count": 6, "id": "ed0819da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total lines: 32764\n", "Total rects: 3162\n", "Found 4128 line clusters.\n", "Extracted 10 valid polygonal areas.\n"]}, {"data": {"image/png": "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*************************************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*********************************************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", "text/plain": ["<Figure size 1200x1200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pdfminer.high_level import extract_pages\n", "from pdfminer.layout import LTLine, LTRect\n", "from shapely.geometry import Polygon, LineString, Point\n", "import matplotlib.pyplot as plt\n", "\n", "# --- Step 1: Extract lines and rectangles from PDF ---\n", "line_elements = []\n", "rect_elements = []\n", "\n", "for page_layout in extract_pages(\"FP1.pdf\"):\n", "    for element in page_layout:\n", "        if isinstance(element, LTLine):\n", "            line_elements.append(element)\n", "        elif isinstance(element, LTRect):\n", "            rect_elements.append(element)\n", "\n", "print(f\"Total lines: {len(line_elements)}\")\n", "print(f\"Total rects: {len(rect_elements)}\")\n", "\n", "# --- Step 2: Cluster connected lines (very basic) ---\n", "def are_lines_connected(line1, line2, tolerance=3):\n", "    \"\"\"Check if any endpoint of line1 is close to any endpoint of line2.\"\"\"\n", "    pts1 = [(line1.x0, line1.y0), (line1.x1, line1.y1)]\n", "    pts2 = [(line2.x0, line2.y0), (line2.x1, line2.y1)]\n", "    for x1, y1 in pts1:\n", "        for x2, y2 in pts2:\n", "            if abs(x1 - x2) < tolerance and abs(y1 - y2) < tolerance:\n", "                return True\n", "    return False\n", "\n", "clusters = []\n", "for line in line_elements:\n", "    added = False\n", "    for cluster in clusters:\n", "        if any(are_lines_connected(line, other) for other in cluster):\n", "            cluster.append(line)\n", "            added = True\n", "            break\n", "    if not added:\n", "        clusters.append([line])\n", "\n", "print(f\"Found {len(clusters)} line clusters.\")\n", "\n", "# --- Step 3: Convert clusters to polygons (naive) ---\n", "polygons = []\n", "for cluster in clusters:\n", "    if len(cluster) >= 4:\n", "        # Attempt to form a polygon using all start points\n", "        points = []\n", "        for l in cluster:\n", "            points.append((l.x0, l.y0))\n", "        points.append((cluster[-1].x1, cluster[-1].y1))  # Close path\n", "        try:\n", "            poly = Polygon(points)\n", "            if poly.is_valid and poly.area > 500:  # Ignore tiny noise\n", "                polygons.append(poly)\n", "        except:\n", "            continue\n", "\n", "print(f\"Extracted {len(polygons)} valid polygonal areas.\")\n", "\n", "# --- Step 4: Plotting the extracted shapes ---\n", "plt.figure(figsize=(12, 12))\n", "for poly in polygons:\n", "    x, y = poly.exterior.xy\n", "    plt.plot(x, y, color=\"blue\")\n", "\n", "for rect in rect_elements:\n", "    x0, y0, x1, y1 = rect.bbox\n", "    rect_poly = Polygon([(x0, y0), (x0, y1), (x1, y1), (x1, y0)])\n", "    x, y = rect_poly.exterior.xy\n", "    plt.plot(x, y, color=\"green\", linestyle='--')\n", "\n", "plt.gca().invert_yaxis()\n", "plt.axis(\"equal\")\n", "plt.title(\"Vector Extraction from PDF Floor Plan\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "028d3c44", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  - Label: 'MEETING - SMALL B' - Code: '08.D.76' - BBox: (560.3230000000001, 2038.31951596, 635.6815639300002, 2061.72328596)\n", "  - Label: 'OPEN OFFICE' - Code: '08.D.79' - BBox: (885.2575, 2129.05371596, 938.0911810300001, 2152.45758596)\n", "  - Label: 'FOCUS A' - Code: '08.D.88' - BBox: (1071.6943999999999, 1985.42061596, 1106.3370996499998, 2008.82438596)\n", "  - Label: 'MEETING - <PERSON>R<PERSON> B' - Code: '08.D.80' - BBox: (771.6725, 1919.03681596, 847.8959434000001, 1942.4406859599999)\n", "  - Label: 'OPEN OFFICE' - Code: '08.D.75' - BBox: (625.4659999999999, 1843.71331596, 678.29968103, 1867.11708596)\n", "  - Label: 'OPEN OFFICE' - Code: '08.D.75' - BBox: (612.2470000000001, 1465.15841596, 665.0806810300002, 1488.56228596)\n", "  - Label: 'MEETING - SMALL A' - Code: '08.C.68' - BBox: (768.4388, 1609.36121596, 843.7973639300001, 1632.7650859599999)\n", "  - Label: 'FOCUS A' - Code: '08.C.64' - BBox: (812.3173, 1471.93181596, 846.9599996500001, 1495.33558596)\n", "  - Label: 'TERRACE' - Code: '08.C.63' - BBox: (456.374, 1550.82461596, 493.61133806000004, 1574.22848596)\n", "  - Label: 'MEETING - SMALL A' - Code: '08.D.82' - BBox: (922.1293, 1919.03681596, 997.48786393, 1942.4406859599999)\n", "  - Label: 'COPY/PRINT' - Code: '08.D.84' - BBox: (910.3742, 1844.96241596, 958.00910004, 1868.36628596)\n", "  - Label: 'ADA RESTROOM' - Code: '08.E.62' - BBox: (919.9224, 1749.01851596, 983.14413884, 1772.42228596)\n", "  - Label: 'RESTROOM' - Code: '08.E.64' - BBox: (929.1107, 1686.03971596, 974.14145746, 1709.4435859599998)\n", "  - Label: 'RESTROOM' - Code: '08.E.66' - BBox: (929.1107, 1625.8344159600001, 974.14145746, 1649.23818596)\n", "  - Label: 'FOCUS A' - Code: '08.D.86' - BBox: (1083.116, 1822.11191596, 1117.75869965, 1845.51578596)\n", "  - Label: 'CIRCULATION' - Code: '08.D.81' - BBox: (1150.3243, 1829.19451596, 1203.14847686, 1852.59838596)\n", "  - Label: 'STAIR B' - Code: '08.E.52' - BBox: (1005.5439, 1767.73761596, 1035.8622023, 1791.14148596)\n", "  - Label: 'CIRCULATION' - Code: '08.E.60' - BBox: (1175.8017, 1639.1468159600001, 1228.62587686, 1662.55068596)\n", "  - Label: 'RESTROOM' - Code: '08.E.68' - BBox: (929.1107, 1565.21971596, 974.14145746, 1588.62358596)\n", "  - Label: 'JAN' - Code: '08.E.70' - BBox: (1123.1716000000001, 1513.37611596, 1150.0398885900001, 1536.77998596)\n", "  - Label: 'IDF STACK A' - Code: '08.E.72' - BBox: (1111.0244, 1466.12601596, 1159.09649186, 1489.52988596)\n", "  - Label: 'PROJECT SPACE' - Code: '08.A.05' - BBox: (1690.92313, 2123.3533159599997, 1755.8841319499998, 2146.75708596)\n", "  - Label: 'TERRACE' - Code: '08.A.07' - BBox: (2046.9789, 2195.93441596, 2084.2162380600003, 2219.33818596)\n", "  - Label: 'STAIR' - Code: '08.D.99' - BBox: (1457.7325, 2076.22501596, 1485.02847624, 2099.62878596)\n", "  - Label: 'MEETING - SMALL A' - Code: '08.D.92' - BBox: (1143.8878, 1909.37881596, 1219.24636393, 1932.78258596)\n", "  - Label: 'FOCUS C' - Code: '08.D.94' - BBox: (1252.3083, 1923.27111596, 1287.3786873, 1946.67488596)\n", "  - Label: 'CIRCULATION' - Code: '08.D.97' - BBox: (1489.8364, 1986.01781596, 1542.66057686, 2009.42158596)\n", "  - Label: 'MOTHER'S ROOM' - Code: '08.D.90' - BBox: (1247.3155, 1844.2491159600002, 1314.18684012, 1867.6528859599998)\n", "  - Label: 'MEETING - SMALL A' - Code: '08.D.96' - BBox: (1331.6165, 1853.73071596, 1406.97506393, 1877.13458596)\n", "  - Label: 'COFFEE POINT' - Code: '08.A.03' - BBox: (1560.1453999999999, 1913.03791596, 1617.7406701999998, 1936.44168596)\n", "  - Label: 'PPE VESTIBULE' - Code: '08.A.04' - BBox: (1756.77229, 1928.07431596, 1817.41839877, 1951.47808596)\n", "  - Label: 'LOCKERS' - Code: '08.A.02' - BBox: (1603.217, 1828.16181596, 1640.46384223, 1851.5656859599999)\n", "  - Label: 'ICT' - Code: '08.A.06' - BBox: (1780.70553, 1836.48411596, 1807.57381859, 1859.88788596)\n", "  - Label: 'OPEN LAB' - Code: '08.A.09' - BBox: (2065.988, 2029.16981596, 2104.97410534, 2052.57368596)\n", "  - Label: 'LAB SUPPORT' - Code: '08.A.10' - BBox: (1957.1636, 1870.07401596, 2011.73654414, 1893.47778596)\n", "  - Label: 'CIRCULATION' - Code: '08.A.08' - BBox: (1946.6055000000001, 1827.32851596, 1999.4296768600002, 1850.73228596)\n", "  - Label: 'ELECTRICAL' - Code: '08.A.22' - BBox: (2073.7294, 1861.55901596, 2121.80149186, 1884.96288596)\n", "  - Label: 'STORAGE' - Code: '08.A.24' - BBox: (2123.1509, 1717.80861596, 2161.2626217, 1741.21238596)\n", "  - Label: 'CHEMICAL STORAGE' - Code: '08.A.26' - BBox: (2096.3009, 1636.5018159600002, 2176.84874075, 1659.90568596)\n", "  - Label: 'COLD STORAGE' - Code: '08.A.28' - BBox: (2095.3392, 1487.3588159600001, 2157.2683717200002, 1510.76268596)\n", "  - Label: 'VIRAL TISSUE CULTURE' - Code: '08.A.30' - BBox: (2063.7719, 1334.06854596, 2155.5821822000003, 1357.47237596)\n", "  - Label: 'STAIR A' - Code: '08.E.02' - BBox: (1871.2708, 1464.45501596, 1901.5891023, 1487.85878596)\n", "  - Label: 'SUPPORT LAB' - Code: '08.B.32' - BBox: (1940.945, 1343.98328596, 1995.5179441399998, 1367.3871159599998)\n", "  - Label: 'ELECTRICAL' - Code: '08.E.08' - BBox: (1752.37541, 1726.9321159600001, 1800.44750186, 1750.33598596)\n", "  - Label: 'IDF STACK B' - Code: '08.E.06' - BBox: (1642.90785, 1651.42891596, 1690.97994186, 1674.83278596)\n", "  - Label: 'FIRE SERVICE LOBBY' - Code: '08.E.04' - BBox: (1734.92053, 1533.0617159600001, 1817.2171380300001, 1556.46558596)\n", "  - Label: 'OPEN LAB' - Code: '08.B.41' - BBox: (1743.35893, 1294.60535746, 1782.34503534, 1318.00918596)\n", "  - Label: 'MAP TESTING TC ROOM' - Code: '08.B.38' - BBox: (1701.91292, 1089.85031596, 1793.71369803, 1113.2540859599999)\n", "  - Label: 'OPEN LAB' - Code: '08.A.11' - BBox: (2366.2545, 1356.98729596, 2405.24060534, 1380.39112596)\n", "  - Label: 'RESTROOM' - Code: '08.E.82' - BBox: (1441.9009, 1782.10821596, 1486.93165746, 1805.51198596)\n", "  - Label: 'ELEVATOR LOBBY' - Code: '08.A.01' - BBox: (1430.4571, 1726.9321159600001, 1500.19869946, 1750.33598596)\n", "  - Label: 'ADA RESTROOM' - Code: '08.E.80' - BBox: (1432.2375, 1691.24121596, 1495.45923884, 1714.64498596)\n", "  - Label: 'ADA RESTROOM' - Code: '08.E.78' - BBox: (1435.9891, 1624.2183159600002, 1499.21083884, 1647.62218596)\n", "  - Label: 'ADA RESTROOM' - Code: '08.E.76' - BBox: (1435.9891, 1551.7138159600001, 1499.21083884, 1575.11758596)\n", "  - Label: 'ADA RESTROOM' - Code: '08.E.74' - BBox: (1435.9891, 1479.68231596, 1499.21083884, 1503.08608596)\n", "  - Label: 'PPE VESTIBULE' - Code: '08.C.53' - BBox: (1152.7828, 1335.87143596, 1213.42890877, 1359.27526596)\n", "  - Label: 'PPE VESTIBULE' - Code: '08.C.52' - BBox: (1152.7828, 1241.20990596, 1213.42890877, 1264.61373596)\n", "  - Label: 'HISTOLOGY CENTER' - Code: '08.C.51' - BBox: (1129.4144999999999, 1095.39441596, 1209.0974612799998, 1118.79828596)\n", "  - Label: 'ELECTRICAL' - Code: '08.C.50' - BBox: (1256.3874, 1297.23224696, 1304.45949186, 1320.63607596)\n", "  - Label: 'PPE VESTIBULE' - Code: '08.B.43' - BBox: (1550.7373, 1335.50124596, 1611.38340877, 1358.90507596)\n", "  - Label: 'PPE VESTIBULE' - Code: '08.B.40' - BBox: (1550.7373, 1241.65085596, 1611.38340877, 1265.05468596)\n", "  - Label: 'MEETING - SMALL A' - Code: '08.C.60' - BBox: (606.517, 1328.41148596, 681.8755639300001, 1351.81531596)\n", "  - Label: 'OPEN OFFICE' - Code: '08.C.61' - BBox: (611.1800000000001, 1190.17721596, 664.0136810300002, 1213.58105596)\n", "  - Label: 'CIRCULATION' - Code: '08.C.55' - BBox: (1057.5679, 1395.40611596, 1110.39207686, 1418.80988596)\n", "  - Label: 'IMAGING' - Code: '08.C.54' - BBox: (1048.1227, 1300.28044696, 1081.9005201799998, 1323.68427596)\n"]}], "source": ["# EXTRACTION OF TEXT AND BBOX AND CODE\n", "\n", "from pdfminer.high_level import extract_pages\n", "from pdfminer.layout import LTTextBox, LTTextLine, LTChar\n", "import re\n", "\n", "code_pattern = re.compile(r'^\\d{2}\\.[A-Z]\\.\\d{2}$')\n", "filtered_pairs = []\n", "for page_layout in extract_pages(\"FP1.pdf\"):\n", "    for element in page_layout:\n", "        if isinstance(element, LTTextBox) or isinstance(element, LTTextLine):\n", "            text = element.get_text().strip()\n", "            x0, y0, x1, y1 = element.bbox\n", "            # print(f\"Text: '{text}' at ({x0}, {y0}, {x1}, {y1})\")\n", "            lines = text.strip().splitlines()\n", "            if len(lines) == 2 and code_pattern.match(lines[1].strip()):\n", "                print(f\"  - Label: '{lines[0]}' - Code: '{lines[1]}' - BBox: ({x0}, {y0}, {x1}, {y1})\")\n", "                filtered_pairs.append({\n", "                    \"label\": lines[0].strip(),\n", "                    \"code\": lines[1].strip(),\n", "                    \"bbox\": ({x0}, {y0}, {x1}, {y1})\n", "                })"]}, {"cell_type": "code", "execution_count": null, "id": "8347973d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Marked PDF saved as: FP1_marked.pdf\n"]}], "source": ["# MARKING EXTRACTED TEXT ROOMS AND CODE\n", "\n", "from pdfminer.high_level import extract_pages\n", "from pdfminer.layout import LTTextBox, LTTextLine\n", "import re\n", "from reportlab.pdfgen import canvas\n", "from reportlab.lib.pagesizes import letter\n", "from PyPDF2 import PdfReader, PdfWriter\n", "\n", "# Step 1: Extract filtered markers\n", "code_pattern1 = re.compile(r'^\\d{2}\\.[A-Z]\\.\\d{2}$')\n", "code_pattern2 = re.compile(r'^\\d{2}\\.[A-Z]\\.\\d{2}.\\d{1}$')\n", "filtered_pairs = []\n", "\n", "pdf_path = \"FP1.pdf\"\n", "\n", "for page_layout in extract_pages(pdf_path):\n", "    for element in page_layout:\n", "        if isinstance(element, (LTTextBox, LTTextLine)):\n", "            text = element.get_text().strip()\n", "            lines = text.splitlines()\n", "            if len(lines) == 2 and (code_pattern1.match(lines[1].strip()) or code_pattern2.match(lines[1].strip())):\n", "                x0, y0, x1, y1 = element.bbox\n", "                filtered_pairs.append({\n", "                    \"label\": lines[0].strip(),\n", "                    \"code\": lines[1].strip(),\n", "                    \"x\": x0,\n", "                    \"y\": y0\n", "                })\n", "\n", "# Step 2: Create overlay with red markers\n", "reader = PdfReader(pdf_path)\n", "page = reader.pages[0]\n", "page_width = float(page.mediabox.width)\n", "page_height = float(page.mediabox.height)\n", "\n", "overlay_path = \"overlay.pdf\"\n", "c = canvas.Canvas(overlay_path, pagesize=(page_width, page_height))\n", "\n", "for item in filtered_pairs:\n", "    x, y = item[\"x\"], item[\"y\"]\n", "    c.setFillColorRGB(1, 0, 0)  # red\n", "    c.circle(x, y, 6, fill=1)   # red circle marker\n", "    <PERSON><PERSON>(\"Helvetica\", 6)\n", "    c.drawString(x + 8, y + 2, item[\"code\"])  # show the code next to the marker\n", "\n", "c.save()\n", "\n", "# Step 3: Merge overlay with original PDF\n", "overlay_reader = PdfReader(overlay_path)\n", "writer = PdfWriter()\n", "\n", "for i, page in enumerate(reader.pages):\n", "    overlay_page = overlay_reader.pages[0]  # same overlay for all pages\n", "    page.merge_page(overlay_page)\n", "    writer.add_page(page)\n", "\n", "output_path = \"FP1_marked.pdf\"\n", "with open(output_path, \"wb\") as f:\n", "    writer.write(f)\n", "\n", "print(f\"✅ Marked PDF saved as: {output_path}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "29af26b4", "metadata": {}, "outputs": [], "source": ["# REMOVING ANY OTHER TEXT\n", "\n", "import fitz  # PyMuPDF\n", "import re\n", "\n", "pdf_path = \"FP1.pdf\"\n", "output_path = \"FP1_cleaned.pdf\"\n", "\n", "doc = fitz.open(pdf_path)\n", "\n", "# Define code pattern\n", "code_pattern1 = re.compile(r'^\\d{2}\\.[A-Z]\\.\\d{2}$')\n", "code_pattern2 = re.compile(r'^\\d{2}\\.[A-Z]\\.\\d{2}\\.\\d{1}$')\n", "\n", "for page in doc:\n", "    blocks = page.get_text(\"dict\")[\"blocks\"]\n", "\n", "    for block in blocks:\n", "        if \"lines\" not in block:\n", "            continue\n", "\n", "        text_lines = block[\"lines\"]\n", "        block_text = \"\\n\".join([\"\".join([span[\"text\"] for span in line[\"spans\"]]) for line in text_lines])\n", "        lines = block_text.strip().splitlines()\n", "\n", "        # Check if it matches the 2-line pattern\n", "        if not (len(lines) == 2 and (code_pattern1.match(lines[1]) or code_pattern2.match(lines[1]))):\n", "            # Redact the whole block (remove it)\n", "            rect = fitz.Rect(block[\"bbox\"])\n", "            page.add_redact_annot(rect, fill=(1, 1, 1))  # White-out\n", "            page.apply_redactions()\n", "\n", "# Save cleaned PDF\n", "doc.save(output_path)\n", "print(f\"✅ Cleaned PDF saved as: {output_path}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "aabaf03d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total lines on first page: 32764\n", "Total rects on first page: 3162\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pdfminer.high_level import extract_pages\n", "from pdfminer.layout import LTRect\n", "\n", "# --- Step 1: Extract lines and rectangles from PDF ---\n", "rect_elements = []\n", "first_page = None # Variable to store the first page layout\n", "\n", "for page_layout in extract_pages(\"FP1.pdf\"):\n", "    first_page = page_layout # Store the page layout\n", "    for element in page_layout:\n", "        if isinstance(element, LTRect):\n", "            rect_elements.append(element)\n", "\n", "print(f\"Total rects on first page: {len(rect_elements)}\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "e57cf09a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}